import { Injectable, Logger } from '@nestjs/common';

export interface QueryIntent {
  primary: 'comparison' | 'specific_need' | 'learning' | 'implementation' | 'exploration';
  urgency: 'immediate' | 'project_based' | 'research';
  techLevel: 'beginner' | 'intermediate' | 'advanced';
  confidence: number; // 0-1 score
}

export interface ExtractedConcepts {
  entityTypes: string[];
  technologies: string[];
  useCases: string[];
  industries: string[];
  features: string[];
  constraints: string[];
}

export interface ImplicitNeeds {
  budgetSensitive: boolean;
  easeOfUse: boolean;
  scalability: boolean;
  integration: boolean;
  support: boolean;
  customization: boolean;
}

export interface ExpandedQuery {
  original: string;
  concepts: ExtractedConcepts;
  implicitNeeds: ImplicitNeeds;
  searchVariants: string[];
  primaryIntent: QueryIntent;
  expandedTerms: string[];
}

@Injectable()
export class QueryUnderstandingService {
  private readonly logger = new Logger(QueryUnderstandingService.name);

  // Entity type patterns for better detection
  private readonly entityTypePatterns = {
    'ai-tool': ['tool', 'software', 'platform', 'app', 'application', 'solution'],
    'course': ['course', 'training', 'class', 'tutorial', 'lesson', 'education'],
    'agency': ['agency', 'company', 'firm', 'service provider', 'consultant'],
    'hardware': ['hardware', 'device', 'computer', 'gpu', 'cpu', 'chip'],
    'software': ['software', 'program', 'application', 'system'],
    'research-paper': ['paper', 'research', 'study', 'publication', 'article'],
    'job': ['job', 'position', 'role', 'career', 'employment', 'work'],
    'event': ['event', 'conference', 'meetup', 'workshop', 'summit'],
    'podcast': ['podcast', 'show', 'episode', 'audio'],
    'community': ['community', 'forum', 'group', 'network'],
    'grant': ['grant', 'funding', 'money', 'investment'],
    'newsletter': ['newsletter', 'news', 'updates', 'digest']
  };

  // Technology and feature patterns
  private readonly technologyPatterns: Record<string, string[]> = {
    'machine-learning': ['ml', 'machine learning', 'ai', 'artificial intelligence'],
    'deep-learning': ['deep learning', 'neural network', 'cnn', 'rnn', 'transformer'],
    'nlp': ['nlp', 'natural language', 'text processing', 'language model'],
    'computer-vision': ['computer vision', 'image recognition', 'cv', 'object detection'],
    'automation': ['automation', 'workflow', 'process', 'automate'],
    'analytics': ['analytics', 'analysis', 'data science', 'insights'],
    'api': ['api', 'integration', 'webhook', 'rest', 'graphql']
  };

  // Use case patterns
  private readonly useCasePatterns: Record<string, string[]> = {
    'content-creation': ['content', 'writing', 'blog', 'article', 'copy'],
    'image-generation': ['image', 'picture', 'photo', 'visual', 'art'],
    'video-editing': ['video', 'editing', 'movie', 'clip'],
    'data-analysis': ['data', 'analysis', 'report', 'dashboard'],
    'customer-service': ['customer', 'support', 'help', 'chat'],
    'marketing': ['marketing', 'advertising', 'promotion', 'campaign'],
    'education': ['education', 'teaching', 'learning', 'student'],
    'development': ['development', 'coding', 'programming', 'software']
  };

  /**
   * Analyze and expand query with comprehensive understanding
   */
  async analyzeAndExpandQuery(originalQuery: string): Promise<ExpandedQuery> {
    this.logger.debug(`Analyzing query: "${originalQuery}"`);

    try {
      // Step 1: Detect primary intent
      const primaryIntent = this.detectQueryIntent(originalQuery);
      
      // Step 2: Extract key concepts
      const concepts = this.extractConcepts(originalQuery);
      
      // Step 3: Infer implicit needs
      const implicitNeeds = this.inferImplicitNeeds(originalQuery, primaryIntent);
      
      // Step 4: Generate expanded terms
      const expandedTerms = this.generateExpandedTerms(originalQuery, concepts);
      
      // Step 5: Create search variants
      const searchVariants = this.generateSearchVariants(originalQuery, concepts, expandedTerms);

      const result: ExpandedQuery = {
        original: originalQuery,
        concepts,
        implicitNeeds,
        searchVariants,
        primaryIntent,
        expandedTerms
      };

      this.logger.debug(`Query analysis completed:`, {
        intent: primaryIntent.primary,
        conceptCount: Object.values(concepts).flat().length,
        variantCount: searchVariants.length
      });

      return result;

    } catch (error) {
      this.logger.error(`Error analyzing query "${originalQuery}":`, error.stack);
      
      // Return basic fallback analysis
      return {
        original: originalQuery,
        concepts: { entityTypes: [], technologies: [], useCases: [], industries: [], features: [], constraints: [] },
        implicitNeeds: { budgetSensitive: false, easeOfUse: false, scalability: false, integration: false, support: false, customization: false },
        searchVariants: [originalQuery],
        primaryIntent: { primary: 'exploration', urgency: 'research', techLevel: 'intermediate', confidence: 0.5 },
        expandedTerms: []
      };
    }
  }

  /**
   * Detect query intent with confidence scoring
   */
  private detectQueryIntent(query: string): QueryIntent {
    const lowerQuery = query.toLowerCase();
    
    // Intent detection patterns
    const intentPatterns = {
      comparison: /\b(compare|vs|versus|between|difference|better|best)\b/i,
      specific_need: /\b(need|want|looking for|help me|find|recommend)\b/i,
      learning: /\b(learn|tutorial|how to|guide|course|teach)\b/i,
      implementation: /\b(implement|build|create|develop|use|integrate)\b/i
    };

    let primary: QueryIntent['primary'] = 'exploration';
    let confidence = 0.5;

    // Check for specific patterns
    for (const [intent, pattern] of Object.entries(intentPatterns)) {
      if (pattern.test(lowerQuery)) {
        primary = intent as QueryIntent['primary'];
        confidence = 0.8;
        break;
      }
    }

    // Detect urgency
    let urgency: QueryIntent['urgency'] = 'research';
    if (/\b(urgent|asap|immediately|now|quick)\b/i.test(lowerQuery)) {
      urgency = 'immediate';
      confidence += 0.1;
    } else if (/\b(project|deadline|soon|this week)\b/i.test(lowerQuery)) {
      urgency = 'project_based';
    }

    // Detect technical level
    let techLevel: QueryIntent['techLevel'] = 'intermediate';
    if (/\b(beginner|new to|simple|easy|basic)\b/i.test(lowerQuery)) {
      techLevel = 'beginner';
    } else if (/\b(advanced|expert|professional|complex|enterprise)\b/i.test(lowerQuery)) {
      techLevel = 'advanced';
    }

    return { primary, urgency, techLevel, confidence: Math.min(1, confidence) };
  }

  /**
   * Extract key concepts from the query
   */
  private extractConcepts(query: string): ExtractedConcepts {
    const lowerQuery = query.toLowerCase();
    
    const concepts: ExtractedConcepts = {
      entityTypes: [],
      technologies: [],
      useCases: [],
      industries: [],
      features: [],
      constraints: []
    };

    // Extract entity types
    for (const [entityType, patterns] of Object.entries(this.entityTypePatterns)) {
      if (patterns.some(pattern => lowerQuery.includes(pattern))) {
        concepts.entityTypes.push(entityType);
      }
    }

    // Extract technologies
    for (const [tech, patterns] of Object.entries(this.technologyPatterns)) {
      if (patterns.some(pattern => lowerQuery.includes(pattern))) {
        concepts.technologies.push(tech);
      }
    }

    // Extract use cases
    for (const [useCase, patterns] of Object.entries(this.useCasePatterns)) {
      if (patterns.some(pattern => lowerQuery.includes(pattern))) {
        concepts.useCases.push(useCase);
      }
    }

    // Extract constraints
    if (/\b(free|open source|no cost)\b/i.test(lowerQuery)) {
      concepts.constraints.push('free');
    }
    if (/\b(enterprise|business|commercial)\b/i.test(lowerQuery)) {
      concepts.constraints.push('enterprise');
    }
    if (/\b(cloud|saas|online)\b/i.test(lowerQuery)) {
      concepts.constraints.push('cloud');
    }

    return concepts;
  }

  /**
   * Infer implicit user needs based on query and intent
   */
  private inferImplicitNeeds(query: string, intent: QueryIntent): ImplicitNeeds {
    const lowerQuery = query.toLowerCase();
    
    return {
      budgetSensitive: /\b(cheap|affordable|budget|cost|price|free)\b/i.test(lowerQuery) || intent.techLevel === 'beginner',
      easeOfUse: /\b(easy|simple|user-friendly|intuitive)\b/i.test(lowerQuery) || intent.techLevel === 'beginner',
      scalability: /\b(scale|growth|enterprise|large)\b/i.test(lowerQuery) || intent.techLevel === 'advanced',
      integration: /\b(integrate|api|connect|workflow)\b/i.test(lowerQuery) || intent.primary === 'implementation',
      support: /\b(support|help|documentation|community)\b/i.test(lowerQuery) || intent.urgency === 'immediate',
      customization: /\b(custom|configure|flexible|adapt)\b/i.test(lowerQuery) || intent.techLevel === 'advanced'
    };
  }

  /**
   * Generate expanded terms for better search coverage
   */
  private generateExpandedTerms(query: string, concepts: ExtractedConcepts): string[] {
    const expandedTerms: string[] = [];
    
    // Add synonyms for detected technologies
    concepts.technologies.forEach(tech => {
      const synonyms = this.technologyPatterns[tech] || [];
      expandedTerms.push(...synonyms);
    });

    // Add related terms for use cases
    concepts.useCases.forEach(useCase => {
      const related = this.useCasePatterns[useCase] || [];
      expandedTerms.push(...related);
    });

    // Remove duplicates and filter out terms already in the original query
    const lowerQuery = query.toLowerCase();
    return [...new Set(expandedTerms)]
      .filter(term => !lowerQuery.includes(term.toLowerCase()))
      .slice(0, 10); // Limit to prevent token overflow
  }

  /**
   * Generate search variants for comprehensive coverage
   */
  private generateSearchVariants(
    originalQuery: string,
    concepts: ExtractedConcepts,
    expandedTerms: string[]
  ): string[] {
    const variants: string[] = [originalQuery];

    // Add entity-type specific variants
    concepts.entityTypes.forEach(entityType => {
      variants.push(`${originalQuery} ${entityType.replace('-', ' ')}`);
    });

    // Add technology-focused variants
    concepts.technologies.forEach(tech => {
      variants.push(`${tech.replace('-', ' ')} ${originalQuery}`);
    });

    // Add use-case variants
    concepts.useCases.forEach(useCase => {
      variants.push(`${originalQuery} for ${useCase.replace('-', ' ')}`);
    });

    // Add expanded term variants (limited)
    if (expandedTerms.length > 0) {
      const topTerms = expandedTerms.slice(0, 3);
      variants.push(`${originalQuery} ${topTerms.join(' ')}`);
    }

    // Remove duplicates and limit variants
    return [...new Set(variants)].slice(0, 8);
  }
}
