-- Add recommendation feedback and interaction tracking tables

-- Table for tracking recommendation interactions
CREATE TABLE IF NOT EXISTS "recommendation_interactions" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "user_id" UUID REFERENCES "users"("id") ON DELETE SET NULL,
    "session_id" VARCHAR(255) NOT NULL,
    "query" TEXT NOT NULL,
    "recommended_entity_ids" TEXT[] NOT NULL,
    "clicked_entity_id" UUID REFERENCES "entities"("id") ON DELETE SET NULL,
    "dwell_time" INTEGER, -- in seconds
    "position" INTEGER, -- position of clicked entity in recommendations
    "user_agent" TEXT,
    "query_intent" VARCHAR(100),
    "diversity_score" DECIMAL(3,2),
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for user preferences based on interactions
CREATE TABLE IF NOT EXISTS "user_preferences" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "user_id" UUID UNIQUE REFERENCES "users"("id") ON DELETE CASCADE,
    "preferred_entity_types" TEXT[] DEFAULT '{}',
    "preferred_categories" TEXT[] DEFAULT '{}',
    "preferred_features" TEXT[] DEFAULT '{}',
    "interaction_count" INTEGER DEFAULT 0,
    "last_interaction_at" TIMESTAMP WITH TIME ZONE,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for query-entity associations
CREATE TABLE IF NOT EXISTS "query_entity_associations" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "query" TEXT NOT NULL,
    "entity_id" UUID REFERENCES "entities"("id") ON DELETE CASCADE,
    "click_count" INTEGER DEFAULT 1,
    "last_clicked_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE("query", "entity_id")
);

-- Table for system events (including model retraining)
CREATE TABLE IF NOT EXISTS "system_events" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "event_type" VARCHAR(100) NOT NULL,
    "description" TEXT,
    "metadata" JSONB,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add popularity_score column to entities table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'entities' AND column_name = 'popularity_score') THEN
        ALTER TABLE "entities" ADD COLUMN "popularity_score" DECIMAL(10,2) DEFAULT 0.0;
    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_recommendation_interactions_user_id" ON "recommendation_interactions"("user_id");
CREATE INDEX IF NOT EXISTS "idx_recommendation_interactions_session_id" ON "recommendation_interactions"("session_id");
CREATE INDEX IF NOT EXISTS "idx_recommendation_interactions_clicked_entity_id" ON "recommendation_interactions"("clicked_entity_id");
CREATE INDEX IF NOT EXISTS "idx_recommendation_interactions_created_at" ON "recommendation_interactions"("created_at");
CREATE INDEX IF NOT EXISTS "idx_recommendation_interactions_query" ON "recommendation_interactions" USING gin(to_tsvector('english', "query"));

CREATE INDEX IF NOT EXISTS "idx_user_preferences_user_id" ON "user_preferences"("user_id");
CREATE INDEX IF NOT EXISTS "idx_user_preferences_last_interaction" ON "user_preferences"("last_interaction_at");

CREATE INDEX IF NOT EXISTS "idx_query_entity_associations_query" ON "query_entity_associations"("query");
CREATE INDEX IF NOT EXISTS "idx_query_entity_associations_entity_id" ON "query_entity_associations"("entity_id");
CREATE INDEX IF NOT EXISTS "idx_query_entity_associations_click_count" ON "query_entity_associations"("click_count");

CREATE INDEX IF NOT EXISTS "idx_system_events_event_type" ON "system_events"("event_type");
CREATE INDEX IF NOT EXISTS "idx_system_events_created_at" ON "system_events"("created_at");

CREATE INDEX IF NOT EXISTS "idx_entities_popularity_score" ON "entities"("popularity_score");

-- Add triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables that need them
DROP TRIGGER IF EXISTS update_recommendation_interactions_updated_at ON "recommendation_interactions";
CREATE TRIGGER update_recommendation_interactions_updated_at 
    BEFORE UPDATE ON "recommendation_interactions" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON "user_preferences";
CREATE TRIGGER update_user_preferences_updated_at 
    BEFORE UPDATE ON "user_preferences" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_query_entity_associations_updated_at ON "query_entity_associations";
CREATE TRIGGER update_query_entity_associations_updated_at 
    BEFORE UPDATE ON "query_entity_associations" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
