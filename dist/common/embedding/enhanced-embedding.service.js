"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EnhancedEmbeddingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedEmbeddingService = void 0;
const common_1 = require("@nestjs/common");
const openai_service_1 = require("../../openai/openai.service");
let EnhancedEmbeddingService = EnhancedEmbeddingService_1 = class EnhancedEmbeddingService {
    constructor(openaiService) {
        this.openaiService = openaiService;
        this.logger = new common_1.Logger(EnhancedEmbeddingService_1.name);
    }
    async generateHybridEmbedding(entity) {
        try {
            const enrichedText = this.buildEnrichedTextRepresentation(entity);
            this.logger.debug(`Generating enhanced embedding for entity: ${entity.name}`);
            this.logger.debug(`Enriched text length: ${enrichedText.length} characters`);
            const embedding = await this.openaiService.generateEmbedding(enrichedText);
            if (!embedding) {
                this.logger.warn(`Failed to generate embedding for entity: ${entity.name}`);
                return null;
            }
            this.logger.debug(`Successfully generated enhanced embedding for: ${entity.name}, dimensions: ${embedding.length}`);
            return embedding;
        }
        catch (error) {
            this.logger.error(`Error generating enhanced embedding for entity ${entity.name}:`, error.stack);
            return null;
        }
    }
    buildEnrichedTextRepresentation(entity) {
        const sections = [];
        sections.push(`${entity.name}`);
        if (entity.shortDescription) {
            sections.push(`Description: ${entity.shortDescription}`);
        }
        if (entity.description && entity.description !== entity.shortDescription) {
            sections.push(`Details: ${entity.description}`);
        }
        sections.push(`Type: ${entity.entityType.name}`);
        if (entity.categories?.length > 0) {
            const categoryNames = entity.categories.map(c => c.category.name).join(', ');
            sections.push(`Categories: ${categoryNames}`);
        }
        if (entity.features?.length > 0) {
            const featureNames = entity.features.map(f => f.feature.name).join(', ');
            sections.push(`Features: ${featureNames}`);
        }
        if (entity.tags?.length > 0) {
            const tagNames = entity.tags.map(t => t.tag.name).join(', ');
            sections.push(`Tags: ${tagNames}`);
        }
        if (entity.useCases && entity.useCases.length > 0) {
            sections.push(`Use cases: ${entity.useCases.join(', ')}`);
        }
        if (entity.targetAudience && entity.targetAudience.length > 0) {
            sections.push(`Target audience: ${entity.targetAudience.join(', ')}`);
        }
        if (entity.platforms && entity.platforms.length > 0) {
            sections.push(`Platforms: ${entity.platforms.join(', ')}`);
        }
        if (entity.integrations && entity.integrations.length > 0) {
            sections.push(`Integrations: ${entity.integrations.join(', ')}`);
        }
        const enrichedText = sections.filter(section => section.trim().length > 0).join('\n');
        if (enrichedText.length > 8000) {
            this.logger.warn(`Enriched text too long (${enrichedText.length} chars), truncating for entity: ${entity.name}`);
            return enrichedText.substring(0, 8000);
        }
        return enrichedText;
    }
    async generateQueryEmbedding(query, expandedTerms) {
        try {
            let enhancedQuery = query;
            if (expandedTerms && expandedTerms.length > 0) {
                enhancedQuery = `${query} ${expandedTerms.join(' ')}`;
            }
            this.logger.debug(`Generating query embedding for: "${query}"`);
            if (expandedTerms && expandedTerms.length > 0) {
                this.logger.debug(`With expanded terms: ${expandedTerms.join(', ')}`);
            }
            const embedding = await this.openaiService.generateEmbedding(enhancedQuery);
            if (!embedding) {
                this.logger.warn(`Failed to generate query embedding for: "${query}"`);
                return null;
            }
            return embedding;
        }
        catch (error) {
            this.logger.error(`Error generating query embedding for "${query}":`, error.stack);
            return null;
        }
    }
    calculateCosineSimilarity(embedding1, embedding2) {
        if (embedding1.length !== embedding2.length) {
            throw new Error('Embeddings must have the same dimensions');
        }
        let dotProduct = 0;
        let norm1 = 0;
        let norm2 = 0;
        for (let i = 0; i < embedding1.length; i++) {
            dotProduct += embedding1[i] * embedding2[i];
            norm1 += embedding1[i] * embedding1[i];
            norm2 += embedding2[i] * embedding2[i];
        }
        const similarity = dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
        return Math.max(0, Math.min(1, similarity));
    }
    async batchGenerateEmbeddings(entities, batchSize = 10, delayMs = 100) {
        const results = [];
        this.logger.log(`Starting batch embedding generation for ${entities.length} entities`);
        for (let i = 0; i < entities.length; i += batchSize) {
            const batch = entities.slice(i, i + batchSize);
            this.logger.debug(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(entities.length / batchSize)}`);
            const batchPromises = batch.map(async (entity) => {
                const embedding = await this.generateHybridEmbedding(entity);
                return { entityId: entity.id, embedding };
            });
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
            if (i + batchSize < entities.length && delayMs > 0) {
                await new Promise(resolve => setTimeout(resolve, delayMs));
            }
        }
        const successCount = results.filter(r => r.embedding !== null).length;
        this.logger.log(`Batch embedding generation completed: ${successCount}/${entities.length} successful`);
        return results;
    }
};
exports.EnhancedEmbeddingService = EnhancedEmbeddingService;
exports.EnhancedEmbeddingService = EnhancedEmbeddingService = EnhancedEmbeddingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [openai_service_1.OpenaiService])
], EnhancedEmbeddingService);
//# sourceMappingURL=enhanced-embedding.service.js.map