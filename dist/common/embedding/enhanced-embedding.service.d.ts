import { OpenaiService } from '../../openai/openai.service';
export interface EntityEmbeddingContext {
    id: string;
    name: string;
    shortDescription?: string;
    description?: string;
    categories: Array<{
        category: {
            name: string;
        };
    }>;
    features: Array<{
        feature: {
            name: string;
        };
    }>;
    tags: Array<{
        tag: {
            name: string;
        };
    }>;
    entityType: {
        name: string;
    };
    useCases?: string[];
    targetAudience?: string[];
    platforms?: string[];
    integrations?: string[];
}
export declare class EnhancedEmbeddingService {
    private readonly openaiService;
    private readonly logger;
    constructor(openaiService: OpenaiService);
    generateHybridEmbedding(entity: EntityEmbeddingContext): Promise<number[] | null>;
    private buildEnrichedTextRepresentation;
    generateQueryEmbedding(query: string, expandedTerms?: string[]): Promise<number[] | null>;
    calculateCosineSimilarity(embedding1: number[], embedding2: number[]): number;
    batchGenerateEmbeddings(entities: EntityEmbeddingContext[], batchSize?: number, delayMs?: number): Promise<Array<{
        entityId: string;
        embedding: number[] | null;
    }>>;
}
