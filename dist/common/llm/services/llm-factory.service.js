"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LlmFactoryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LlmFactoryService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../../prisma/prisma.service");
const openai_llm_service_1 = require("./openai-llm.service");
const google_gemini_llm_service_1 = require("./google-gemini-llm.service");
const anthropic_llm_service_1 = require("./anthropic-llm.service");
const enhanced_anthropic_llm_service_1 = require("../../../chat/services/enhanced-anthropic-llm.service");
let LlmFactoryService = LlmFactoryService_1 = class LlmFactoryService {
    constructor(prisma, openaiLlmService, googleGeminiLlmService, anthropicLlmService, enhancedAnthropicLlmService) {
        this.prisma = prisma;
        this.openaiLlmService = openaiLlmService;
        this.googleGeminiLlmService = googleGeminiLlmService;
        this.anthropicLlmService = anthropicLlmService;
        this.enhancedAnthropicLlmService = enhancedAnthropicLlmService;
        this.logger = new common_1.Logger(LlmFactoryService_1.name);
    }
    async getLlmService() {
        try {
            const setting = await this.prisma.appSetting.findUnique({
                where: { key: 'CURRENT_LLM_PROVIDER' },
            });
            const provider = setting?.value || 'OPENAI';
            this.logger.log(`Using LLM provider: ${provider}`);
            switch (provider) {
                case 'OPENAI':
                    return this.openaiLlmService;
                case 'GOOGLE_GEMINI':
                    return this.googleGeminiLlmService;
                case 'ANTHROPIC':
                    return this.anthropicLlmService;
                case 'ANTHROPIC_ENHANCED':
                default:
                    return this.enhancedAnthropicLlmService;
            }
        }
        catch (error) {
            this.logger.error('Error getting LLM provider setting, defaulting to Anthropic', error.stack);
            return this.enhancedAnthropicLlmService;
        }
    }
    getLlmServiceByProvider(provider) {
        switch (provider.toUpperCase()) {
            case 'OPENAI':
                return this.openaiLlmService;
            case 'GOOGLE_GEMINI':
                return this.googleGeminiLlmService;
            case 'ANTHROPIC':
                return this.anthropicLlmService;
            case 'ANTHROPIC_ENHANCED':
            default:
                return this.enhancedAnthropicLlmService;
        }
    }
    getAvailableProviders() {
        return ['OPENAI', 'GOOGLE_GEMINI', 'ANTHROPIC', 'ANTHROPIC_ENHANCED'];
    }
};
exports.LlmFactoryService = LlmFactoryService;
exports.LlmFactoryService = LlmFactoryService = LlmFactoryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        openai_llm_service_1.OpenaiLlmService,
        google_gemini_llm_service_1.GoogleGeminiLlmService,
        anthropic_llm_service_1.AnthropicLlmService,
        enhanced_anthropic_llm_service_1.EnhancedAnthropicLlmService])
], LlmFactoryService);
//# sourceMappingURL=llm-factory.service.js.map