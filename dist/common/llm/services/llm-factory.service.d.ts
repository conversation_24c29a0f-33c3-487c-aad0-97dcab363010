import { PrismaService } from '../../../prisma/prisma.service';
import { ILlmService } from '../interfaces/llm.service.interface';
import { OpenaiLlmService } from './openai-llm.service';
import { GoogleGeminiLlmService } from './google-gemini-llm.service';
import { AnthropicLlmService } from './anthropic-llm.service';
import { EnhancedAnthropicLlmService } from '../../../chat/services/enhanced-anthropic-llm.service';
export declare class LlmFactoryService {
    private readonly prisma;
    private readonly openaiLlmService;
    private readonly googleGeminiLlmService;
    private readonly anthropicLlmService;
    private readonly enhancedAnthropicLlmService;
    private readonly logger;
    constructor(prisma: PrismaService, openaiLlmService: OpenaiLlmService, googleGeminiLlmService: GoogleGeminiLlmService, anthropicLlmService: AnthropicLlmService, enhancedAnthropicLlmService: EnhancedAnthropicLlmService);
    getLlmService(): Promise<ILlmService>;
    getLlmServiceByProvider(provider: string): ILlmService;
    getAvailableProviders(): string[];
}
