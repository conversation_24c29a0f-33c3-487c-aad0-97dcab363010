import { OpenaiService } from '../../../openai/openai.service';
import { ILlmService, LlmRecommendation, CandidateEntity, ChatResponse, ConversationContext, UserIntent } from '../interfaces/llm.service.interface';
import { SharedPromptBuilderService } from './shared-prompt-builder.service';
export declare class OpenaiLlmService implements ILlmService {
    private readonly openaiService;
    private readonly sharedPromptBuilder;
    private readonly logger;
    constructor(openaiService: OpenaiService, sharedPromptBuilder: SharedPromptBuilderService);
    getRecommendation(problemDescription: string, candidateEntities: CandidateEntity[]): Promise<LlmRecommendation>;
    private buildRecommendationPrompt;
    private parseOpenAIResponse;
    private getFallbackRecommendation;
    private detectQueryIntent;
    private getRecommendationStrategy;
    private extractUniqueStrengths;
    getChatResponse(userMessage: string, context: ConversationContext, candidateEntities?: CandidateEntity[]): Promise<ChatResponse>;
    classifyIntent(userMessage: string, context: ConversationContext): Promise<UserIntent>;
    generateFollowUpQuestions(context: ConversationContext): Promise<string[]>;
    shouldTransitionToRecommendations(context: ConversationContext): Promise<{
        shouldTransition: boolean;
        reason: string;
    }>;
    private buildChatPrompt;
    private buildIntentClassificationPrompt;
    private buildFollowUpPrompt;
    private buildTransitionPrompt;
    private parseChatResponse;
    private validateDiscoveredEntities;
    private validateMessageContentForHallucination;
    private isGenericTerm;
    private getGenericReplacement;
    private parseIntentResponse;
    private parseFollowUpResponse;
    private parseTransitionResponse;
    private formatEntitiesForChat;
    private formatUserProfile;
    private extractDiscussedTopics;
    private extractPreviousQuestions;
    private getFallbackChatResponse;
    private createContextualFallbackMessage;
    private extractUserInfoFromMessages;
    private getFallbackIntent;
    private getFallbackFollowUpQuestions;
}
