import { PrismaService } from '../../prisma/prisma.service';
export interface InteractionData {
    userId?: string;
    sessionId: string;
    query: string;
    recommendedEntityIds: string[];
    clickedEntityId?: string;
    dwellTime?: number;
    position?: number;
    timestamp: Date;
    userAgent?: string;
    queryIntent?: string;
    diversityScore?: number;
}
export interface PersonalizationScore {
    entityId: string;
    userId: string;
    categoryPreference: number;
    typePreference: number;
    featurePreference: number;
    overallScore: number;
}
export declare class RecommendationFeedbackService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    trackInteraction(interactionData: InteractionData): Promise<void>;
    private updateEntityScore;
    private getScoreIncrement;
    private updateUserPreferences;
    private updateQueryEntityAssociations;
    getPersonalizedBoost(userId: string, entityId: string): Promise<number>;
    getPopularEntities(limit?: number, timeWindowDays?: number): Promise<string[]>;
    getQualityMetrics(timeWindowDays?: number): Promise<{
        clickThroughRate: number;
        averagePosition: number;
        zeroClickRate: number;
        averageDwellTime: number;
        totalInteractions: number;
    }>;
    shouldRetrainModel(): Promise<boolean>;
    triggerModelRetraining(): Promise<void>;
}
