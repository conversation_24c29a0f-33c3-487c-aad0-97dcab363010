export interface QueryIntent {
    primary: 'comparison' | 'specific_need' | 'learning' | 'implementation' | 'exploration';
    urgency: 'immediate' | 'project_based' | 'research';
    techLevel: 'beginner' | 'intermediate' | 'advanced';
    confidence: number;
}
export interface ExtractedConcepts {
    entityTypes: string[];
    technologies: string[];
    useCases: string[];
    industries: string[];
    features: string[];
    constraints: string[];
}
export interface ImplicitNeeds {
    budgetSensitive: boolean;
    easeOfUse: boolean;
    scalability: boolean;
    integration: boolean;
    support: boolean;
    customization: boolean;
}
export interface ExpandedQuery {
    original: string;
    concepts: ExtractedConcepts;
    implicitNeeds: ImplicitNeeds;
    searchVariants: string[];
    primaryIntent: QueryIntent;
    expandedTerms: string[];
}
export declare class QueryUnderstandingService {
    private readonly logger;
    private readonly entityTypePatterns;
    private readonly technologyPatterns;
    private readonly useCasePatterns;
    analyzeAndExpandQuery(originalQuery: string): Promise<ExpandedQuery>;
    private detectQueryIntent;
    private extractConcepts;
    private inferImplicitNeeds;
    private generateExpandedTerms;
    private generateSearchVariants;
}
