"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var QueryUnderstandingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryUnderstandingService = void 0;
const common_1 = require("@nestjs/common");
let QueryUnderstandingService = QueryUnderstandingService_1 = class QueryUnderstandingService {
    constructor() {
        this.logger = new common_1.Logger(QueryUnderstandingService_1.name);
        this.entityTypePatterns = {
            'ai-tool': ['tool', 'software', 'platform', 'app', 'application', 'solution'],
            'course': ['course', 'training', 'class', 'tutorial', 'lesson', 'education'],
            'agency': ['agency', 'company', 'firm', 'service provider', 'consultant'],
            'hardware': ['hardware', 'device', 'computer', 'gpu', 'cpu', 'chip'],
            'software': ['software', 'program', 'application', 'system'],
            'research-paper': ['paper', 'research', 'study', 'publication', 'article'],
            'job': ['job', 'position', 'role', 'career', 'employment', 'work'],
            'event': ['event', 'conference', 'meetup', 'workshop', 'summit'],
            'podcast': ['podcast', 'show', 'episode', 'audio'],
            'community': ['community', 'forum', 'group', 'network'],
            'grant': ['grant', 'funding', 'money', 'investment'],
            'newsletter': ['newsletter', 'news', 'updates', 'digest']
        };
        this.technologyPatterns = {
            'machine-learning': ['ml', 'machine learning', 'ai', 'artificial intelligence'],
            'deep-learning': ['deep learning', 'neural network', 'cnn', 'rnn', 'transformer'],
            'nlp': ['nlp', 'natural language', 'text processing', 'language model'],
            'computer-vision': ['computer vision', 'image recognition', 'cv', 'object detection'],
            'automation': ['automation', 'workflow', 'process', 'automate'],
            'analytics': ['analytics', 'analysis', 'data science', 'insights'],
            'api': ['api', 'integration', 'webhook', 'rest', 'graphql']
        };
        this.useCasePatterns = {
            'content-creation': ['content', 'writing', 'blog', 'article', 'copy'],
            'image-generation': ['image', 'picture', 'photo', 'visual', 'art'],
            'video-editing': ['video', 'editing', 'movie', 'clip'],
            'data-analysis': ['data', 'analysis', 'report', 'dashboard'],
            'customer-service': ['customer', 'support', 'help', 'chat'],
            'marketing': ['marketing', 'advertising', 'promotion', 'campaign'],
            'education': ['education', 'teaching', 'learning', 'student'],
            'development': ['development', 'coding', 'programming', 'software']
        };
    }
    async analyzeAndExpandQuery(originalQuery) {
        this.logger.debug(`Analyzing query: "${originalQuery}"`);
        try {
            const primaryIntent = this.detectQueryIntent(originalQuery);
            const concepts = this.extractConcepts(originalQuery);
            const implicitNeeds = this.inferImplicitNeeds(originalQuery, primaryIntent);
            const expandedTerms = this.generateExpandedTerms(originalQuery, concepts);
            const searchVariants = this.generateSearchVariants(originalQuery, concepts, expandedTerms);
            const result = {
                original: originalQuery,
                concepts,
                implicitNeeds,
                searchVariants,
                primaryIntent,
                expandedTerms
            };
            this.logger.debug(`Query analysis completed:`, {
                intent: primaryIntent.primary,
                conceptCount: Object.values(concepts).flat().length,
                variantCount: searchVariants.length
            });
            return result;
        }
        catch (error) {
            this.logger.error(`Error analyzing query "${originalQuery}":`, error.stack);
            return {
                original: originalQuery,
                concepts: { entityTypes: [], technologies: [], useCases: [], industries: [], features: [], constraints: [] },
                implicitNeeds: { budgetSensitive: false, easeOfUse: false, scalability: false, integration: false, support: false, customization: false },
                searchVariants: [originalQuery],
                primaryIntent: { primary: 'exploration', urgency: 'research', techLevel: 'intermediate', confidence: 0.5 },
                expandedTerms: []
            };
        }
    }
    detectQueryIntent(query) {
        const lowerQuery = query.toLowerCase();
        const intentPatterns = {
            comparison: /\b(compare|vs|versus|between|difference|better|best)\b/i,
            specific_need: /\b(need|want|looking for|help me|find|recommend)\b/i,
            learning: /\b(learn|tutorial|how to|guide|course|teach)\b/i,
            implementation: /\b(implement|build|create|develop|use|integrate)\b/i
        };
        let primary = 'exploration';
        let confidence = 0.5;
        for (const [intent, pattern] of Object.entries(intentPatterns)) {
            if (pattern.test(lowerQuery)) {
                primary = intent;
                confidence = 0.8;
                break;
            }
        }
        let urgency = 'research';
        if (/\b(urgent|asap|immediately|now|quick)\b/i.test(lowerQuery)) {
            urgency = 'immediate';
            confidence += 0.1;
        }
        else if (/\b(project|deadline|soon|this week)\b/i.test(lowerQuery)) {
            urgency = 'project_based';
        }
        let techLevel = 'intermediate';
        if (/\b(beginner|new to|simple|easy|basic)\b/i.test(lowerQuery)) {
            techLevel = 'beginner';
        }
        else if (/\b(advanced|expert|professional|complex|enterprise)\b/i.test(lowerQuery)) {
            techLevel = 'advanced';
        }
        return { primary, urgency, techLevel, confidence: Math.min(1, confidence) };
    }
    extractConcepts(query) {
        const lowerQuery = query.toLowerCase();
        const concepts = {
            entityTypes: [],
            technologies: [],
            useCases: [],
            industries: [],
            features: [],
            constraints: []
        };
        for (const [entityType, patterns] of Object.entries(this.entityTypePatterns)) {
            if (patterns.some(pattern => lowerQuery.includes(pattern))) {
                concepts.entityTypes.push(entityType);
            }
        }
        for (const [tech, patterns] of Object.entries(this.technologyPatterns)) {
            if (patterns.some(pattern => lowerQuery.includes(pattern))) {
                concepts.technologies.push(tech);
            }
        }
        for (const [useCase, patterns] of Object.entries(this.useCasePatterns)) {
            if (patterns.some(pattern => lowerQuery.includes(pattern))) {
                concepts.useCases.push(useCase);
            }
        }
        if (/\b(free|open source|no cost)\b/i.test(lowerQuery)) {
            concepts.constraints.push('free');
        }
        if (/\b(enterprise|business|commercial)\b/i.test(lowerQuery)) {
            concepts.constraints.push('enterprise');
        }
        if (/\b(cloud|saas|online)\b/i.test(lowerQuery)) {
            concepts.constraints.push('cloud');
        }
        return concepts;
    }
    inferImplicitNeeds(query, intent) {
        const lowerQuery = query.toLowerCase();
        return {
            budgetSensitive: /\b(cheap|affordable|budget|cost|price|free)\b/i.test(lowerQuery) || intent.techLevel === 'beginner',
            easeOfUse: /\b(easy|simple|user-friendly|intuitive)\b/i.test(lowerQuery) || intent.techLevel === 'beginner',
            scalability: /\b(scale|growth|enterprise|large)\b/i.test(lowerQuery) || intent.techLevel === 'advanced',
            integration: /\b(integrate|api|connect|workflow)\b/i.test(lowerQuery) || intent.primary === 'implementation',
            support: /\b(support|help|documentation|community)\b/i.test(lowerQuery) || intent.urgency === 'immediate',
            customization: /\b(custom|configure|flexible|adapt)\b/i.test(lowerQuery) || intent.techLevel === 'advanced'
        };
    }
    generateExpandedTerms(query, concepts) {
        const expandedTerms = [];
        concepts.technologies.forEach(tech => {
            const synonyms = this.technologyPatterns[tech] || [];
            expandedTerms.push(...synonyms);
        });
        concepts.useCases.forEach(useCase => {
            const related = this.useCasePatterns[useCase] || [];
            expandedTerms.push(...related);
        });
        const lowerQuery = query.toLowerCase();
        return [...new Set(expandedTerms)]
            .filter(term => !lowerQuery.includes(term.toLowerCase()))
            .slice(0, 10);
    }
    generateSearchVariants(originalQuery, concepts, expandedTerms) {
        const variants = [originalQuery];
        concepts.entityTypes.forEach(entityType => {
            variants.push(`${originalQuery} ${entityType.replace('-', ' ')}`);
        });
        concepts.technologies.forEach(tech => {
            variants.push(`${tech.replace('-', ' ')} ${originalQuery}`);
        });
        concepts.useCases.forEach(useCase => {
            variants.push(`${originalQuery} for ${useCase.replace('-', ' ')}`);
        });
        if (expandedTerms.length > 0) {
            const topTerms = expandedTerms.slice(0, 3);
            variants.push(`${originalQuery} ${topTerms.join(' ')}`);
        }
        return [...new Set(variants)].slice(0, 8);
    }
};
exports.QueryUnderstandingService = QueryUnderstandingService;
exports.QueryUnderstandingService = QueryUnderstandingService = QueryUnderstandingService_1 = __decorate([
    (0, common_1.Injectable)()
], QueryUnderstandingService);
//# sourceMappingURL=query-understanding.service.js.map