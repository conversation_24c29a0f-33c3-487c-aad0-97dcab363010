export interface RerankingContext {
    query: string;
    userContext?: {
        technicalLevel?: string;
        previousInteractions?: string[];
        preferredCategories?: string[];
        budgetSensitive?: boolean;
    };
    queryIntent?: {
        primary: string;
        urgency: string;
        techLevel: string;
    };
    diversityWeight?: number;
}
export interface EntitySimilarityScore {
    entityId: string;
    relevanceScore: number;
    diversityScore: number;
    interactionScore: number;
    qualityScore: number;
    finalScore: number;
}
export declare class ContextualRerankingService {
    private readonly logger;
    rerankResults(entities: any[], context: RerankingContext, maxResults?: number): Promise<any[]>;
    private groupByEntityType;
    private applyMMR;
    private adjustLambdaForContext;
    private calculateRelevanceScore;
    private calculateDiversityScore;
    private applyInteractionBoost;
    private applyTimeDecay;
    private applyQualityThresholds;
    calculateResultSetDiversity(entities: any[]): number;
}
