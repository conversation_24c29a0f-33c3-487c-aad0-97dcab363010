import { RecommendationFiltersDto } from '../dto/recommendation-filters.dto';
import { EntitiesService } from '../../entities/entities.service';
export declare class FilterExtractionService {
    private readonly entitiesService;
    private readonly logger;
    constructor(entitiesService: EntitiesService);
    extractFiltersFromDescription(description: string): Promise<Partial<RecommendationFiltersDto>>;
    private extractEntityTypes;
    private extractTechnicalLevels;
    private extractBudgetFilters;
    private extractPlatforms;
    private extractFrameworks;
    private extractUseCases;
    private extractKeyFeatures;
    private extractJobFilters;
    private extractCourseFilters;
    private extractEventFilters;
    private extractHardwareFilters;
    private matchesAny;
}
