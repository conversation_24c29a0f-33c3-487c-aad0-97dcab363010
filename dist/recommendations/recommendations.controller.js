"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecommendationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const throttler_1 = require("@nestjs/throttler");
const supabase_auth_guard_1 = require("../auth/guards/supabase-auth.guard");
const recommendations_service_1 = require("./recommendations.service");
const create_recommendation_dto_1 = require("./dto/create-recommendation.dto");
const recommendation_response_dto_1 = require("./dto/recommendation-response.dto");
let RecommendationsController = class RecommendationsController {
    constructor(recommendationsService) {
        this.recommendationsService = recommendationsService;
    }
    async getRecommendations(createRecommendationDto) {
        return this.recommendationsService.getRecommendations(createRecommendationDto);
    }
    async debugRecommendations(body) {
        return this.recommendationsService.debugRecommendationPipeline(body.problem_description);
    }
    async getQualityMetrics(timeWindowDays) {
        const timeWindow = timeWindowDays ? parseInt(timeWindowDays, 10) : 7;
        const metrics = await this.recommendationsService.getQualityMetrics(timeWindow);
        return {
            ...metrics,
            timeWindow,
            generatedAt: new Date(),
            qualityGrade: this.calculateQualityGrade(metrics),
        };
    }
    async trackInteraction(body) {
        const userId = undefined;
        await this.recommendationsService.trackRecommendationInteraction(userId, body.sessionId, body.query, body.recommendedEntityIds, body.clickedEntityId, body.position, body.dwellTime);
        return {
            success: true,
            message: 'Interaction tracked successfully',
        };
    }
    async getDashboardMetrics(timeWindowDays) {
        const timeWindow = timeWindowDays ? parseInt(timeWindowDays, 10) : 7;
        const qualityMetrics = await this.recommendationsService.getQualityMetrics(timeWindow);
        const qualityGrade = this.calculateQualityGrade(qualityMetrics);
        const recommendations = this.generateRecommendationImprovements(qualityMetrics);
        return {
            timeWindow,
            generatedAt: new Date(),
            qualityMetrics,
            qualityGrade,
            recommendations,
            targets: {
                clickThroughRate: 0.4,
                averagePosition: 3,
                zeroClickRate: 0.1,
                averageDwellTime: 30,
            },
            status: this.getSystemStatus(qualityMetrics),
        };
    }
    calculateQualityGrade(metrics) {
        let score = 0;
        let maxScore = 0;
        if (metrics.totalInteractions > 0) {
            score += Math.min(metrics.clickThroughRate / 0.4, 1) * 40;
        }
        maxScore += 40;
        if (metrics.averagePosition > 0) {
            score += Math.max(0, (5 - metrics.averagePosition) / 5) * 30;
        }
        maxScore += 30;
        if (metrics.totalInteractions > 0) {
            score += Math.max(0, (1 - metrics.zeroClickRate / 0.1)) * 20;
        }
        maxScore += 20;
        if (metrics.averageDwellTime > 0) {
            score += Math.min(metrics.averageDwellTime / 30, 1) * 10;
        }
        maxScore += 10;
        const percentage = maxScore > 0 ? (score / maxScore) * 100 : 0;
        if (percentage >= 90)
            return 'A+';
        if (percentage >= 80)
            return 'A';
        if (percentage >= 70)
            return 'B';
        if (percentage >= 60)
            return 'C';
        if (percentage >= 50)
            return 'D';
        return 'F';
    }
    generateRecommendationImprovements(metrics) {
        const recommendations = [];
        if (metrics.clickThroughRate < 0.3) {
            recommendations.push('Consider improving recommendation relevance - CTR is below target');
        }
        if (metrics.averagePosition > 3) {
            recommendations.push('Users are clicking on lower-ranked results - review ranking algorithm');
        }
        if (metrics.zeroClickRate > 0.15) {
            recommendations.push('High zero-click rate - consider improving result quality or query understanding');
        }
        if (metrics.averageDwellTime < 20) {
            recommendations.push('Low dwell time suggests poor result quality - review recommendation accuracy');
        }
        if (metrics.totalInteractions < 100) {
            recommendations.push('Low interaction volume - consider promoting the recommendation feature');
        }
        if (recommendations.length === 0) {
            recommendations.push('Metrics look good! Continue monitoring and consider A/B testing new features');
        }
        return recommendations;
    }
    getSystemStatus(metrics) {
        const grade = this.calculateQualityGrade(metrics);
        if (['A+', 'A'].includes(grade))
            return 'excellent';
        if (['B', 'C'].includes(grade))
            return 'good';
        if (grade === 'D')
            return 'needs_attention';
        return 'critical';
    }
};
exports.RecommendationsController = RecommendationsController;
__decorate([
    (0, common_1.Post)(),
    (0, throttler_1.Throttle)({ default: { limit: 10, ttl: 60000 } }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Get AI-powered recommendations with comprehensive filtering',
        description: `
    Get personalized AI recommendations based on a problem description with access to 80+ filter parameters.

    This enhanced endpoint:
    1. Uses vector search to find semantically relevant entities
    2. Applies comprehensive filters across all entity types (Tools, Courses, Jobs, Events, Hardware, etc.)
    3. Uses the configured LLM provider to analyze and recommend the best options
    4. Returns detailed recommendations with explanations

    **NEW: Enhanced Filtering Capabilities**
    - Tool filters: technical_levels, learning_curves, has_api, frameworks, platforms, etc.
    - Course filters: skill_levels, certificate_available, instructor_name, duration, etc.
    - Job filters: employment_types, experience_levels, salary ranges, location_types, etc.
    - Event filters: event_types, is_online, location, date ranges, registration_required, etc.
    - Hardware filters: hardware_types, manufacturers, price ranges, memory/processor specs, etc.
    - And 60+ more entity-specific filters for comprehensive discovery

    The LLM provider can be configured by admins via the admin settings API.
    `,
    }),
    (0, swagger_1.ApiBody)({
        type: create_recommendation_dto_1.CreateRecommendationDto,
        description: 'Problem description and comprehensive filters for precise recommendations',
        examples: {
            'Beginner AI Tool with API': {
                summary: 'AI tool for beginners with API access',
                value: {
                    problem_description: 'I need an AI tool to help me generate code documentation automatically for my Python projects',
                    filters: {
                        entityTypeIds: ['ai-tool'],
                        technical_levels: ['BEGINNER', 'INTERMEDIATE'],
                        has_api: true,
                        has_free_tier: true,
                        frameworks: ['Python'],
                        platforms: ['Web', 'Linux'],
                        use_cases_search: 'documentation',
                        max_candidates: 15,
                    },
                },
            },
            'Senior ML Job Remote': {
                summary: 'Senior ML engineering job, remote, high salary',
                value: {
                    problem_description: 'I want to find a senior machine learning engineering position that pays well and allows remote work',
                    filters: {
                        entityTypeIds: ['job'],
                        experience_levels: ['SENIOR', 'LEAD'],
                        employment_types: ['FULL_TIME'],
                        location_types: ['Remote'],
                        salary_min: 120,
                        job_description: 'machine learning',
                        max_candidates: 20,
                    },
                },
            },
            'AI Course with Certificate': {
                summary: 'AI course for beginners with certificate',
                value: {
                    problem_description: 'I am a beginner and want to learn about artificial intelligence with a certificate',
                    filters: {
                        entityTypeIds: ['course'],
                        skill_levels: ['BEGINNER'],
                        certificate_available: true,
                        searchTerm: 'artificial intelligence',
                        duration_text: 'weeks',
                        max_candidates: 10,
                    },
                },
            },
            'AI Conference Online 2024': {
                summary: 'Online AI conferences in 2024',
                value: {
                    problem_description: 'I want to attend AI conferences to learn about the latest developments',
                    filters: {
                        entityTypeIds: ['event'],
                        event_types: ['Conference'],
                        is_online: true,
                        start_date_from: '2024-01-01',
                        start_date_to: '2024-12-31',
                        searchTerm: 'artificial intelligence',
                        max_candidates: 15,
                    },
                },
            },
            'GPU Under $2000': {
                summary: 'GPU for AI development under $2000',
                value: {
                    problem_description: 'I need a powerful GPU for training machine learning models on a budget',
                    filters: {
                        entityTypeIds: ['hardware'],
                        hardware_types: ['GPU'],
                        price_max: 2000,
                        memory_search: '16GB',
                        manufacturers: ['NVIDIA'],
                        max_candidates: 10,
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'AI recommendations generated successfully',
        type: recommendation_response_dto_1.RecommendationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid request data',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Authentication required',
    }),
    (0, swagger_1.ApiResponse)({
        status: 429,
        description: 'Rate limit exceeded',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error during recommendation generation',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_recommendation_dto_1.CreateRecommendationDto]),
    __metadata("design:returntype", Promise)
], RecommendationsController.prototype, "getRecommendations", null);
__decorate([
    (0, common_1.Post)('debug'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Debug recommendation pipeline',
        description: 'Debug endpoint to check each step of the recommendation pipeline',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Debug information returned successfully',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RecommendationsController.prototype, "debugRecommendations", null);
__decorate([
    (0, common_1.Get)('metrics/quality'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Get recommendation quality metrics',
        description: 'Retrieve comprehensive quality metrics including CTR, average position, and user satisfaction scores',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Quality metrics returned successfully',
        schema: {
            type: 'object',
            properties: {
                clickThroughRate: { type: 'number', description: 'Click-through rate (0-1)' },
                averagePosition: { type: 'number', description: 'Average position of clicked recommendations' },
                zeroClickRate: { type: 'number', description: 'Rate of searches with no clicks (0-1)' },
                averageDwellTime: { type: 'number', description: 'Average time spent on recommended entities (seconds)' },
                totalInteractions: { type: 'number', description: 'Total number of recommendation interactions' },
                timeWindow: { type: 'number', description: 'Time window in days for the metrics' },
            },
        },
    }),
    __param(0, (0, common_1.Query)('timeWindowDays')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RecommendationsController.prototype, "getQualityMetrics", null);
__decorate([
    (0, common_1.Post)('track-interaction'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Track user interaction with recommendations',
        description: 'Record user clicks, dwell time, and other interaction data for learning and improvement',
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                sessionId: { type: 'string', description: 'Session identifier' },
                query: { type: 'string', description: 'Original search query' },
                recommendedEntityIds: { type: 'array', items: { type: 'string' }, description: 'List of recommended entity IDs' },
                clickedEntityId: { type: 'string', description: 'ID of clicked entity (optional)' },
                position: { type: 'number', description: 'Position of clicked entity in results (optional)' },
                dwellTime: { type: 'number', description: 'Time spent on entity page in seconds (optional)' },
            },
            required: ['sessionId', 'query', 'recommendedEntityIds'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Interaction tracked successfully',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RecommendationsController.prototype, "trackInteraction", null);
__decorate([
    (0, common_1.Get)('metrics/dashboard'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Get comprehensive metrics dashboard data',
        description: 'Retrieve all metrics needed for a recommendation quality dashboard',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dashboard metrics returned successfully',
    }),
    __param(0, (0, common_1.Query)('timeWindowDays')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RecommendationsController.prototype, "getDashboardMetrics", null);
exports.RecommendationsController = RecommendationsController = __decorate([
    (0, swagger_1.ApiTags)('Recommendations'),
    (0, common_1.Controller)('recommendations'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.SupabaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [recommendations_service_1.RecommendationsService])
], RecommendationsController);
//# sourceMappingURL=recommendations.controller.js.map