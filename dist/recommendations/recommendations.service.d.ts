import { EntitiesService } from '../entities/entities.service';
import { ILlmService } from '../common/llm/interfaces/llm.service.interface';
import { LlmFactoryService } from '../common/llm/services/llm-factory.service';
import { CreateRecommendationDto } from './dto/create-recommendation.dto';
import { RecommendationResponseDto } from './dto/recommendation-response.dto';
import { FilterExtractionService } from './services/filter-extraction.service';
import { QueryUnderstandingService } from './services/query-understanding.service';
import { ContextualRerankingService } from './services/contextual-reranking.service';
import { RecommendationFeedbackService } from './services/recommendation-feedback.service';
import { AdvancedEntityRankingService } from '../common/ranking/advanced-entity-ranking.service';
import { PerformanceOptimizationService } from '../common/performance/performance-optimization.service';
import { QueryOptimizationService } from '../common/performance/query-optimization.service';
export declare class RecommendationsService {
    private readonly entitiesService;
    private readonly llmService;
    private readonly llmFactoryService;
    private readonly filterExtractionService;
    private readonly queryUnderstandingService;
    private readonly contextualRerankingService;
    private readonly feedbackService;
    private readonly advancedRankingService;
    private readonly performanceOptimizationService;
    private readonly queryOptimizationService;
    private readonly logger;
    constructor(entitiesService: EntitiesService, llmService: ILlmService, llmFactoryService: LlmFactoryService, filterExtractionService: FilterExtractionService, queryUnderstandingService: QueryUnderstandingService, contextualRerankingService: ContextualRerankingService, feedbackService: RecommendationFeedbackService, advancedRankingService: AdvancedEntityRankingService, performanceOptimizationService: PerformanceOptimizationService, queryOptimizationService: QueryOptimizationService);
    getRecommendations(createRecommendationDto: CreateRecommendationDto): Promise<RecommendationResponseDto>;
    private findCandidateEntities;
    private mergeFilters;
    private extractFilterConfidence;
    private extractUserPreferences;
    private generateQuerySignature;
    private convertToLlmCandidates;
    private getRecommendedEntityDetails;
    private mapToEntityListItemResponseDto;
    private getCurrentLlmProvider;
    private getFallbackCandidates;
    private tryLowerThresholdVectorSearch;
    private tryWithoutEntityTypeFilters;
    private tryKeywordBasedSearch;
    private tryMinimalFilters;
    private extractKeywords;
    debugRecommendationPipeline(problemDescription: string): Promise<any>;
    private enhanceFiltersWithQueryAnalysis;
    private findCandidateEntitiesWithQueryVariants;
    private calculateDiversityWeight;
    trackRecommendationInteraction(userId: string | undefined, sessionId: string, query: string, recommendedEntityIds: string[], clickedEntityId?: string, position?: number, dwellTime?: number, userAgent?: string): Promise<void>;
    getPersonalizedRecommendations(createRecommendationDto: CreateRecommendationDto, userId?: string): Promise<RecommendationResponseDto>;
    getQualityMetrics(timeWindowDays?: number): Promise<any>;
    private applyFiltersToEntities;
}
