// Quick test to verify the recommendations endpoint fix
const axios = require('axios');

const testData = {
  problem_description: "I need an AI tool to help me generate code documentation automatically for my Python projects",
  filters: {
    max_candidates: 15,
    entity_type_ids: ["e35dea27-b628-40fc-99c5-e09ae63fb135"], // Using snake_case
    has_free_tier: true
  }
};

const testDataCamelCase = {
  problem_description: "I need an AI tool to help me generate code documentation automatically for my Python projects",
  filters: {
    max_candidates: 15,
    entityTypeIds: ["e35dea27-b628-40fc-99c5-e09ae63fb135"], // Using camelCase
    has_free_tier: true
  }
};

async function testRecommendations() {
  const baseURL = 'http://localhost:3001';
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.baNr0VvVPPfFwslBNsEDR1iw0Y7ybfwBrRGP26Vj5IV';

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };

  console.log('Testing snake_case entity_type_ids...');
  try {
    const response1 = await axios.post(`${baseURL}/recommendations`, testData, { headers });
    console.log('✅ snake_case test passed:', response1.status);
  } catch (error) {
    console.log('❌ snake_case test failed:', error.response?.status, error.response?.data?.message);
  }

  console.log('\nTesting camelCase entityTypeIds...');
  try {
    const response2 = await axios.post(`${baseURL}/recommendations`, testDataCamelCase, { headers });
    console.log('✅ camelCase test passed:', response2.status);
  } catch (error) {
    console.log('❌ camelCase test failed:', error.response?.status, error.response?.data?.message);
  }
}

testRecommendations().catch(console.error);
