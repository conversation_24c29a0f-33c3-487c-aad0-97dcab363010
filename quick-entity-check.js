#!/usr/bin/env node

/**
 * Quick check of entities in database for debugging chat system
 */

const { PrismaClient } = require('@prisma/client');

async function quickEntityCheck() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Quick Entity Database Check...\n');
    
    // Check total entities
    const totalEntities = await prisma.entity.count();
    console.log(`📊 Total entities in database: ${totalEntities}`);
    
    // Check active entities
    const activeEntities = await prisma.entity.count({
      where: { status: 'ACTIVE' }
    });
    console.log(`✅ Active entities: ${activeEntities}`);
    
    // Check entities with embeddings
    const entitiesWithEmbeddings = await prisma.entity.count({
      where: { 
        status: 'ACTIVE',
        vectorEmbedding: { not: null }
      }
    });
    console.log(`🎯 Active entities with embeddings: ${entitiesWithEmbeddings}`);
    
    // Check for coding-related entities
    const codingEntities = await prisma.entity.findMany({
      where: {
        status: 'ACTIVE',
        OR: [
          { name: { contains: 'code', mode: 'insensitive' } },
          { name: { contains: 'coding', mode: 'insensitive' } },
          { name: { contains: 'copilot', mode: 'insensitive' } },
          { name: { contains: 'cursor', mode: 'insensitive' } },
          { name: { contains: 'tabnine', mode: 'insensitive' } },
          { shortDescription: { contains: 'code', mode: 'insensitive' } },
          { shortDescription: { contains: 'programming', mode: 'insensitive' } },
          { shortDescription: { contains: 'development', mode: 'insensitive' } },
        ]
      },
      select: {
        id: true,
        name: true,
        shortDescription: true,
        vectorEmbedding: true
      },
      take: 10
    });
    
    console.log(`\n🔍 Coding-related entities found: ${codingEntities.length}`);
    if (codingEntities.length > 0) {
      codingEntities.forEach((entity, index) => {
        const hasEmbedding = entity.vectorEmbedding ? '✅' : '❌';
        console.log(`   ${index + 1}. ${entity.name} ${hasEmbedding}`);
        console.log(`      ${entity.shortDescription?.substring(0, 80)}...`);
      });
    } else {
      console.log('   ❌ No coding-related entities found!');
      console.log('   This explains why chat returns generic tools instead of database entities.');
    }
    
    // Sample some entities to see what we have
    console.log('\n📋 Sample entities in database:');
    const sampleEntities = await prisma.entity.findMany({
      where: { status: 'ACTIVE' },
      select: {
        id: true,
        name: true,
        shortDescription: true,
        vectorEmbedding: true
      },
      take: 5,
      orderBy: { createdAt: 'desc' }
    });
    
    sampleEntities.forEach((entity, index) => {
      const hasEmbedding = entity.vectorEmbedding ? '✅' : '❌';
      console.log(`   ${index + 1}. ${entity.name} ${hasEmbedding}`);
      console.log(`      ${entity.shortDescription?.substring(0, 80)}...`);
    });
    
    console.log('\n🎯 DIAGNOSIS:');
    if (entitiesWithEmbeddings === 0) {
      console.log('❌ NO ENTITIES HAVE EMBEDDINGS - Vector search will return 0 results');
      console.log('   Solution: Run embedding generation script');
    } else if (codingEntities.length === 0) {
      console.log('❌ NO CODING-RELATED ENTITIES - Chat queries for "AI coding" return nothing');
      console.log('   Solution: Add coding tools like GitHub Copilot, Cursor, Tabnine, etc.');
    } else {
      console.log('✅ Database looks good - issue might be elsewhere in the pipeline');
    }
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
quickEntityCheck().catch(console.error);
