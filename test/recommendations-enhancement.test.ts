import { Test, TestingModule } from '@nestjs/testing';
import { QueryUnderstandingService } from '../src/recommendations/services/query-understanding.service';
import { ContextualRerankingService } from '../src/recommendations/services/contextual-reranking.service';
import { RecommendationFeedbackService } from '../src/recommendations/services/recommendation-feedback.service';
import { PrismaService } from '../src/prisma/prisma.service';

describe('Enhanced Recommendations System', () => {
  let queryUnderstandingService: QueryUnderstandingService;
  let contextualRerankingService: ContextualRerankingService;
  let feedbackService: RecommendationFeedbackService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QueryUnderstandingService,
        ContextualRerankingService,
        RecommendationFeedbackService,
        {
          provide: PrismaService,
          useValue: {
            // Mock Prisma service
            recommendationInteraction: {
              create: jest.fn(),
              findMany: jest.fn(),
              groupBy: jest.fn(),
              count: jest.fn(),
            },
            entity: {
              update: jest.fn(),
              findUnique: jest.fn(),
            },
            userPreferences: {
              upsert: jest.fn(),
              findUnique: jest.fn(),
            },
            queryEntityAssociation: {
              upsert: jest.fn(),
            },
            systemEvent: {
              create: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    queryUnderstandingService = module.get<QueryUnderstandingService>(QueryUnderstandingService);
    contextualRerankingService = module.get<ContextualRerankingService>(ContextualRerankingService);
    feedbackService = module.get<RecommendationFeedbackService>(RecommendationFeedbackService);
  });

  describe('Query Understanding Service', () => {
    it('should detect comparison intent correctly', async () => {
      const query = 'compare ChatGPT vs Claude for writing tasks';
      const analysis = await queryUnderstandingService.analyzeAndExpandQuery(query);

      expect(analysis.primaryIntent.primary).toBe('comparison');
      expect(analysis.concepts.entityTypes).toContain('ai-tool');
      expect(analysis.concepts.useCases).toContain('content-creation');
      expect(analysis.searchVariants.length).toBeGreaterThan(1);
    });

    it('should detect specific need intent', async () => {
      const query = 'I need an AI tool for image generation';
      const analysis = await queryUnderstandingService.analyzeAndExpandQuery(query);

      expect(analysis.primaryIntent.primary).toBe('specific_need');
      expect(analysis.concepts.entityTypes).toContain('ai-tool');
      expect(analysis.concepts.useCases).toContain('image-generation');
      expect(analysis.implicitNeeds.easeOfUse).toBe(true);
    });

    it('should detect learning intent', async () => {
      const query = 'how to learn machine learning for beginners';
      const analysis = await queryUnderstandingService.analyzeAndExpandQuery(query);

      expect(analysis.primaryIntent.primary).toBe('learning');
      expect(analysis.primaryIntent.techLevel).toBe('beginner');
      expect(analysis.concepts.entityTypes).toContain('course');
      expect(analysis.implicitNeeds.easeOfUse).toBe(true);
    });

    it('should expand query terms correctly', async () => {
      const query = 'AI writing assistant';
      const analysis = await queryUnderstandingService.analyzeAndExpandQuery(query);

      expect(analysis.expandedTerms.length).toBeGreaterThan(0);
      expect(analysis.searchVariants).toContain(query);
      expect(analysis.searchVariants.length).toBeGreaterThan(1);
    });
  });

  describe('Contextual Re-ranking Service', () => {
    const mockEntities = [
      {
        id: '1',
        name: 'ChatGPT',
        entityType: { name: 'AI Tool' },
        categories: [{ category: { name: 'Writing' } }],
        features: [{ feature: { name: 'Text Generation' } }],
        avgRating: 4.5,
        reviewCount: 1000,
        similarity: 0.9,
      },
      {
        id: '2',
        name: 'Claude',
        entityType: { name: 'AI Tool' },
        categories: [{ category: { name: 'Writing' } }],
        features: [{ feature: { name: 'Text Generation' } }],
        avgRating: 4.3,
        reviewCount: 500,
        similarity: 0.85,
      },
      {
        id: '3',
        name: 'Midjourney',
        entityType: { name: 'AI Tool' },
        categories: [{ category: { name: 'Image Generation' } }],
        features: [{ feature: { name: 'Art Creation' } }],
        avgRating: 4.7,
        reviewCount: 800,
        similarity: 0.7,
      },
    ];

    it('should apply MMR for diversity', async () => {
      const context = {
        query: 'AI tools for creative work',
        queryIntent: { primary: 'exploration', urgency: 'research', techLevel: 'intermediate' },
        diversityWeight: 0.3,
      };

      const reranked = await contextualRerankingService.rerankResults(mockEntities, context, 3);

      expect(reranked.length).toBeLessThanOrEqual(3);
      expect(reranked[0]).toBeDefined();
      
      // Should include diverse entity types
      const entityTypes = reranked.map(e => e.entityType.name);
      const uniqueTypes = new Set(entityTypes);
      expect(uniqueTypes.size).toBeGreaterThan(1); // Should have diversity
    });

    it('should calculate diversity score correctly', () => {
      const diversityScore = contextualRerankingService.calculateResultSetDiversity(mockEntities);
      
      expect(diversityScore).toBeGreaterThan(0);
      expect(diversityScore).toBeLessThanOrEqual(1);
    });

    it('should prioritize relevance for specific needs', async () => {
      const context = {
        query: 'best AI writing tool',
        queryIntent: { primary: 'specific_need', urgency: 'immediate', techLevel: 'beginner' },
        diversityWeight: 0.1, // Low diversity, high relevance
      };

      const reranked = await contextualRerankingService.rerankResults(mockEntities, context, 2);

      expect(reranked.length).toBeLessThanOrEqual(2);
      // Should prioritize higher similarity scores for specific needs
      expect(reranked[0].similarity).toBeGreaterThanOrEqual(reranked[1]?.similarity || 0);
    });
  });

  describe('Recommendation Feedback Service', () => {
    it('should track interactions without errors', async () => {
      const interactionData = {
        userId: 'user-123',
        sessionId: 'session-456',
        query: 'AI writing tools',
        recommendedEntityIds: ['1', '2', '3'],
        clickedEntityId: '1',
        position: 1,
        dwellTime: 45,
        timestamp: new Date(),
      };

      // Should not throw an error
      await expect(feedbackService.trackInteraction(interactionData)).resolves.not.toThrow();
    });

    it('should calculate quality metrics', async () => {
      const metrics = await feedbackService.getQualityMetrics(7);

      expect(metrics).toHaveProperty('clickThroughRate');
      expect(metrics).toHaveProperty('averagePosition');
      expect(metrics).toHaveProperty('zeroClickRate');
      expect(metrics).toHaveProperty('averageDwellTime');
      expect(metrics).toHaveProperty('totalInteractions');
    });

    it('should determine retrain conditions', async () => {
      const shouldRetrain = await feedbackService.shouldRetrainModel();
      expect(typeof shouldRetrain).toBe('boolean');
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete recommendation enhancement flow', async () => {
      // 1. Query Understanding
      const query = 'I need a free AI tool for content creation';
      const analysis = await queryUnderstandingService.analyzeAndExpandQuery(query);

      expect(analysis.primaryIntent.primary).toBe('specific_need');
      expect(analysis.implicitNeeds.budgetSensitive).toBe(true);

      // 2. Contextual Re-ranking
      const context = {
        query,
        queryIntent: analysis.primaryIntent,
        userContext: {
          technicalLevel: analysis.primaryIntent.techLevel,
          budgetSensitive: analysis.implicitNeeds.budgetSensitive,
        },
        diversityWeight: 0.25,
      };

      const mockEntities = [
        {
          id: '1',
          name: 'Free AI Writer',
          entityType: { name: 'AI Tool' },
          categories: [{ category: { name: 'Writing' } }],
          features: [{ feature: { name: 'Content Creation' } }],
          avgRating: 4.0,
          reviewCount: 200,
          similarity: 0.8,
        },
      ];

      const reranked = await contextualRerankingService.rerankResults(mockEntities, context, 5);
      expect(reranked.length).toBeGreaterThan(0);

      // 3. Feedback Tracking
      const interactionData = {
        sessionId: 'test-session',
        query,
        recommendedEntityIds: reranked.map(e => e.id),
        clickedEntityId: reranked[0].id,
        position: 1,
        timestamp: new Date(),
      };

      await expect(feedbackService.trackInteraction(interactionData)).resolves.not.toThrow();
    });
  });
});
