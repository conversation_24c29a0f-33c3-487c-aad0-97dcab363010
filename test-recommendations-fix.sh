#!/bin/bash

# Test script to verify the recommendations fix
# This script tests if the API is now using the enhanced recommendation system instead of just returning latest entities

BASE_URL=${1:-"http://localhost:3000"}
JWT_TOKEN="eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I0fLFMETin1-dB7MEgbcN2LTLHHzKTyUJ2NsBw1Yhsc"

echo "🔧 Testing Enhanced Recommendations Fix"
echo "======================================"
echo "Base URL: $BASE_URL"
echo ""

# Test 1: Check if the API is using enhanced recommendations
echo "1️⃣ Testing enhanced recommendations system..."
echo "Query: 'I need a free AI tool for content creation'"

RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}\n" \
  -X POST "$BASE_URL/recommendations" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "problem_description": "I need a free AI tool for content creation"
  }')

HTTP_STATUS=$(echo "$RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
BODY=$(echo "$RESPONSE" | sed '/HTTP_STATUS:/d')

echo "Status: $HTTP_STATUS"

if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "201" ]; then
  echo "✅ API responded successfully"
  
  # Check for enhanced features
  if echo "$BODY" | grep -q "metadata"; then
    echo "✅ Enhanced features detected (metadata field present)"
  else
    echo "⚠️  Enhanced features may not be active (no metadata field)"
  fi
  
  if echo "$BODY" | grep -q "diversity"; then
    echo "✅ Diversity scoring active"
  else
    echo "⚠️  Diversity scoring not detected"
  fi
  
  if echo "$BODY" | grep -q "optimized for relevance"; then
    echo "✅ Enhanced explanation detected"
  else
    echo "⚠️  Standard explanation (enhanced system may not be active)"
  fi
  
  # Check if we're getting actual recommendations vs just latest entities
  ENTITY_COUNT=$(echo "$BODY" | grep -o '"id"' | wc -l)
  echo "📊 Returned $ENTITY_COUNT entities"
  
  if [ "$ENTITY_COUNT" -gt 0 ]; then
    echo "✅ Entities returned"
    
    # Check if explanation mentions specific reasoning
    if echo "$BODY" | grep -q "Based on your need"; then
      echo "✅ Contextual explanation detected"
    else
      echo "⚠️  Generic explanation (may be fallback mode)"
    fi
  else
    echo "❌ No entities returned"
  fi
  
else
  echo "❌ API request failed"
  echo "Error response:"
  echo "$BODY"
  exit 1
fi

echo ""
echo "----------------------------------------"

# Test 2: Test debug endpoint to see what's happening
echo "2️⃣ Testing debug endpoint..."

DEBUG_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}\n" \
  -X POST "$BASE_URL/recommendations/debug" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "problem_description": "AI writing tools"
  }')

DEBUG_STATUS=$(echo "$DEBUG_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
DEBUG_BODY=$(echo "$DEBUG_RESPONSE" | sed '/HTTP_STATUS:/d')

if [ "$DEBUG_STATUS" = "200" ]; then
  echo "✅ Debug endpoint working"
  
  # Check vector search results
  if echo "$DEBUG_BODY" | grep -q "step2_vector_search"; then
    VECTOR_COUNT=$(echo "$DEBUG_BODY" | grep -o '"id"' | wc -l)
    echo "📊 Vector search returned results: $VECTOR_COUNT entities"
    
    if [ "$VECTOR_COUNT" -gt 0 ]; then
      echo "✅ Vector search is working"
    else
      echo "❌ Vector search returned no results"
    fi
  fi
  
  # Check embedding generation
  if echo "$DEBUG_BODY" | grep -q "step1_embedding_generation"; then
    echo "✅ Embedding generation tested"
  fi
  
  # Check embedding coverage
  if echo "$DEBUG_BODY" | grep -q "withEmbeddings"; then
    echo "✅ Embedding coverage checked"
  fi
  
else
  echo "❌ Debug endpoint failed: $DEBUG_STATUS"
fi

echo ""
echo "----------------------------------------"

# Test 3: Compare with different queries to see if results vary
echo "3️⃣ Testing query variation..."

QUERIES=(
  "machine learning tools"
  "image generation AI"
  "chatbot platforms"
)

for query in "${QUERIES[@]}"; do
  echo ""
  echo "Testing: '$query'"
  
  RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}\n" \
    -X POST "$BASE_URL/recommendations" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -d "{\"problem_description\": \"$query\"}")
  
  HTTP_STATUS=$(echo "$RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
  BODY=$(echo "$RESPONSE" | sed '/HTTP_STATUS:/d')
  
  if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "201" ]; then
    ENTITY_COUNT=$(echo "$BODY" | grep -o '"id"' | wc -l)
    echo "  ✅ Status: $HTTP_STATUS, Entities: $ENTITY_COUNT"
    
    # Check if results are different (indicating it's not just returning latest)
    FIRST_ENTITY_ID=$(echo "$BODY" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    echo "  📝 First entity ID: $FIRST_ENTITY_ID"
  else
    echo "  ❌ Status: $HTTP_STATUS"
  fi
done

echo ""
echo "======================================"
echo "🎯 Fix Verification Summary"
echo "======================================"

if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "201" ]; then
  echo "✅ API is responding"
  
  if echo "$BODY" | grep -q "metadata"; then
    echo "✅ Enhanced recommendation system is active"
  else
    echo "❌ Enhanced recommendation system may not be active"
  fi
  
  if [ "$ENTITY_COUNT" -gt 0 ]; then
    echo "✅ Entities are being returned"
  else
    echo "❌ No entities returned"
  fi
  
  echo ""
  echo "🚀 Next steps:"
  echo "1. Check server logs for any errors"
  echo "2. Verify vector embeddings exist in database"
  echo "3. Test with different queries to ensure variation"
  echo "4. Monitor response times and quality"
  
else
  echo "❌ API is not working properly"
  echo ""
  echo "🔧 Troubleshooting steps:"
  echo "1. Check if server is running"
  echo "2. Verify JWT token is valid"
  echo "3. Check server logs for errors"
  echo "4. Ensure database is accessible"
fi

echo ""
echo "💡 To run this test:"
echo "   chmod +x test-recommendations-fix.sh"
echo "   ./test-recommendations-fix.sh [your-server-url]"
