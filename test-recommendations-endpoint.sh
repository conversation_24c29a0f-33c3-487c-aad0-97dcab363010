#!/bin/bash

# Test script for the enhanced recommendations endpoint
# Usage: ./test-recommendations-endpoint.sh [BASE_URL]

BASE_URL=${1:-"http://localhost:3000"}
JWT_TOKEN="eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I0fLFMETin1-dB7MEgbcN2LTLHHzKTyUJ2NsBw1Yhsc"

echo "🧪 Testing Enhanced Recommendations System"
echo "=========================================="
echo "Base URL: $BASE_URL"
echo ""

# Test 1: Basic recommendation request
echo "1️⃣ Testing basic recommendations..."
echo "Query: 'I need a free AI tool for content creation'"

RESPONSE1=$(curl -s -w "\nHTTP_STATUS:%{http_code}\n" \
  -X POST "$BASE_URL/recommendations" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "problem_description": "I need a free AI tool for content creation"
  }')

HTTP_STATUS1=$(echo "$RESPONSE1" | grep "HTTP_STATUS:" | cut -d: -f2)
BODY1=$(echo "$RESPONSE1" | sed '/HTTP_STATUS:/d')

if [ "$HTTP_STATUS1" = "200" ] || [ "$HTTP_STATUS1" = "201" ]; then
  echo "✅ Status: $HTTP_STATUS1"
  echo "✅ Response received"
  
  # Check if response contains expected fields
  if echo "$BODY1" | grep -q "recommended_entities"; then
    echo "✅ Contains recommended_entities"
  else
    echo "❌ Missing recommended_entities"
  fi
  
  if echo "$BODY1" | grep -q "explanation"; then
    echo "✅ Contains explanation"
  else
    echo "❌ Missing explanation"
  fi
  
  if echo "$BODY1" | grep -q "metadata"; then
    echo "✅ Contains metadata (enhanced features working)"
  else
    echo "⚠️  Missing metadata (enhanced features may not be active)"
  fi
  
  # Pretty print the response
  echo ""
  echo "📄 Response preview:"
  echo "$BODY1" | head -20
  
else
  echo "❌ Status: $HTTP_STATUS1"
  echo "❌ Error response:"
  echo "$BODY1"
fi

echo ""
echo "----------------------------------------"

# Test 2: Different query types
echo "2️⃣ Testing different query intents..."

QUERIES=(
  "compare ChatGPT vs Claude for writing"
  "how to learn machine learning for beginners"
  "best AI image generation tools"
  "I need enterprise AI software for my business"
)

for query in "${QUERIES[@]}"; do
  echo ""
  echo "Testing: '$query'"
  
  RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}\n" \
    -X POST "$BASE_URL/recommendations" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -d "{\"problem_description\": \"$query\"}")
  
  HTTP_STATUS=$(echo "$RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
  
  if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "201" ]; then
    echo "✅ Status: $HTTP_STATUS"
  else
    echo "❌ Status: $HTTP_STATUS"
  fi
done

echo ""
echo "----------------------------------------"

# Test 3: Quality metrics endpoint
echo "3️⃣ Testing quality metrics endpoint..."

METRICS_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}\n" \
  -X GET "$BASE_URL/recommendations/metrics/quality" \
  -H "Authorization: Bearer $JWT_TOKEN")

METRICS_STATUS=$(echo "$METRICS_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
METRICS_BODY=$(echo "$METRICS_RESPONSE" | sed '/HTTP_STATUS:/d')

if [ "$METRICS_STATUS" = "200" ]; then
  echo "✅ Quality metrics endpoint working"
  echo "📊 Metrics preview:"
  echo "$METRICS_BODY"
else
  echo "❌ Quality metrics endpoint failed: $METRICS_STATUS"
  echo "$METRICS_BODY"
fi

echo ""
echo "----------------------------------------"

# Test 4: Interaction tracking
echo "4️⃣ Testing interaction tracking..."

TRACK_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}\n" \
  -X POST "$BASE_URL/recommendations/track-interaction" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "sessionId": "test-session-123",
    "query": "AI writing tools",
    "recommendedEntityIds": ["entity-1", "entity-2", "entity-3"],
    "clickedEntityId": "entity-1",
    "position": 1,
    "dwellTime": 45
  }')

TRACK_STATUS=$(echo "$TRACK_RESPONSE" | grep "HTTP_STATUS:" | cut -d: -f2)
TRACK_BODY=$(echo "$TRACK_RESPONSE" | sed '/HTTP_STATUS:/d')

if [ "$TRACK_STATUS" = "200" ]; then
  echo "✅ Interaction tracking working"
  echo "📝 Response: $TRACK_BODY"
else
  echo "❌ Interaction tracking failed: $TRACK_STATUS"
  echo "$TRACK_BODY"
fi

echo ""
echo "=========================================="
echo "🎯 Test Summary"
echo "=========================================="

if [ "$HTTP_STATUS1" = "200" ] || [ "$HTTP_STATUS1" = "201" ]; then
  echo "✅ Basic recommendations: WORKING"
else
  echo "❌ Basic recommendations: FAILED"
fi

if [ "$METRICS_STATUS" = "200" ]; then
  echo "✅ Quality metrics: WORKING"
else
  echo "❌ Quality metrics: FAILED"
fi

if [ "$TRACK_STATUS" = "200" ]; then
  echo "✅ Interaction tracking: WORKING"
else
  echo "❌ Interaction tracking: FAILED"
fi

echo ""
echo "🚀 Enhanced Recommendations System Test Complete!"
echo ""
echo "💡 To run this test:"
echo "   chmod +x test-recommendations-endpoint.sh"
echo "   ./test-recommendations-endpoint.sh [your-server-url]"
